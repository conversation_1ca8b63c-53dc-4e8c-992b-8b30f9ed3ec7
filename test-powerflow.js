// Node.js测试脚本 - 简化版潮流计算测试

// 复数类
class Complex {
  constructor(real, imag) {
    this.real = real;
    this.imag = imag;
  }

  static fromPolar(magnitude, angle) {
    return new Complex(
      magnitude * Math.cos(angle),
      magnitude * Math.sin(angle)
    );
  }

  magnitude() {
    return Math.sqrt(this.real * this.real + this.imag * this.imag);
  }

  angle() {
    return Math.atan2(this.imag, this.real);
  }

  add(other) {
    return new Complex(this.real + other.real, this.imag + other.imag);
  }

  multiply(other) {
    return new Complex(
      this.real * other.real - this.imag * other.imag,
      this.real * other.imag + this.imag * other.real
    );
  }

  conjugate() {
    return new Complex(this.real, -this.imag);
  }
}

// 构建导纳矩阵
function buildAdmittanceMatrix(nodes, branches) {
  const n = nodes.length;
  const Y = Array(n).fill(null).map(() => Array(n).fill(null).map(() => new Complex(0, 0)));

  branches.forEach(branch => {
    const i = branch.from - 1;
    const j = branch.to - 1;
    
    if (i < 0 || i >= n || j < 0 || j >= n) {
      console.warn(`支路节点索引无效: from=${branch.from}, to=${branch.to}`);
      return;
    }
    
    const impedance = new Complex(branch.resistance, branch.reactance);
    const impedanceMagnitudeSquared = impedance.real * impedance.real + impedance.imag * impedance.imag;
    
    if (impedanceMagnitudeSquared < 1e-12) {
      console.warn(`支路阻抗过小或为零: R=${branch.resistance}, X=${branch.reactance}`);
      return;
    }
    
    const admittance = new Complex(
      impedance.real / impedanceMagnitudeSquared,
      -impedance.imag / impedanceMagnitudeSquared
    );

    Y[i][j] = Y[i][j].add(new Complex(-admittance.real, -admittance.imag));
    Y[j][i] = Y[j][i].add(new Complex(-admittance.real, -admittance.imag));
    Y[i][i] = Y[i][i].add(admittance);
    Y[j][j] = Y[j][j].add(admittance);
    
    if (branch.susceptance !== 0) {
      const halfSusceptance = new Complex(0, branch.susceptance / 2);
      Y[i][i] = Y[i][i].add(halfSusceptance);
      Y[j][j] = Y[j][j].add(halfSusceptance);
    }
  });

  return Y;
}

// 计算功率不平衡量
function calculateMismatch(nodes, Y) {
  const n = nodes.length;
  const deltaP = [];
  const deltaQ = [];

  for (let i = 0; i < n; i++) {
    if (nodes[i].type === 'slack') continue;

    let Pcalc = 0;
    let Qcalc = 0;

    for (let j = 0; j < n; j++) {
      const Yij = Y[i][j];
      const Vi = nodes[i].voltage;
      const Vj = nodes[j].voltage;
      const thetaij = nodes[i].angle - nodes[j].angle;

      Pcalc += Vi * Vj * (Yij.real * Math.cos(thetaij) + Yij.imag * Math.sin(thetaij));
      Qcalc += Vi * Vj * (Yij.imag * Math.cos(thetaij) - Yij.real * Math.sin(thetaij));
    }

    deltaP.push(nodes[i].activePower - Pcalc);
    
    if (nodes[i].type === 'pq') {
      deltaQ.push(nodes[i].reactivePower - Qcalc);
    }
  }

  return { deltaP, deltaQ };
}

// 测试基础3节点系统
function testBasicSystem() {
  console.log('=== 测试基础3节点系统 ===');
  
  const nodes = [
    { id: 1, type: 'slack', voltage: 1.0, angle: 0, activePower: 0, reactivePower: 0, name: '平衡节点' },
    { id: 2, type: 'pq', voltage: 1.0, angle: 0, activePower: -0.5, reactivePower: -0.2, name: '负荷节点1' },
    { id: 3, type: 'pq', voltage: 1.0, angle: 0, activePower: -0.3, reactivePower: -0.1, name: '负荷节点2' }
  ];

  const branches = [
    { from: 1, to: 2, resistance: 0.02, reactance: 0.06, susceptance: 0 },
    { from: 1, to: 3, resistance: 0.03, reactance: 0.09, susceptance: 0 },
    { from: 2, to: 3, resistance: 0.025, reactance: 0.075, susceptance: 0 }
  ];
  
  console.log('构建导纳矩阵...');
  const Y = buildAdmittanceMatrix(nodes, branches);
  
  console.log('导纳矩阵:');
  Y.forEach((row, i) => {
    const rowStr = row.map(y => `${y.real.toFixed(4)}${y.imag >= 0 ? '+' : ''}${y.imag.toFixed(4)}j`).join('  ');
    console.log(`Y[${i}]: [${rowStr}]`);
  });
  
  console.log('\n计算功率不平衡量...');
  const { deltaP, deltaQ } = calculateMismatch(nodes, Y);
  
  console.log('功率不平衡量:');
  console.log('ΔP:', deltaP.map(p => p.toFixed(6)));
  console.log('ΔQ:', deltaQ.map(q => q.toFixed(6)));
  
  const maxMismatch = Math.max(...deltaP.map(Math.abs), ...deltaQ.map(Math.abs));
  console.log(`最大不平衡量: ${maxMismatch.toExponential(3)}`);
  
  return { nodes, Y, deltaP, deltaQ, maxMismatch };
}

// 测试重负荷系统
function testHeavyLoadSystem() {
  console.log('\n=== 测试重负荷系统 ===');

  const nodes = [
    { id: 1, type: 'slack', voltage: 1.0, angle: 0, activePower: 0, reactivePower: 0, name: '平衡节点' },
    { id: 2, type: 'pq', voltage: 1.0, angle: 0, activePower: -0.9, reactivePower: -0.3, name: '重负荷节点' },
    { id: 3, type: 'pq', voltage: 1.0, angle: 0, activePower: -0.6, reactivePower: -0.2, name: '负荷节点' }
  ];

  const branches = [
    { from: 1, to: 2, resistance: 0.02, reactance: 0.06, susceptance: 0 },
    { from: 1, to: 3, resistance: 0.03, reactance: 0.09, susceptance: 0 },
    { from: 2, to: 3, resistance: 0.025, reactance: 0.075, susceptance: 0 }
  ];

  const Y = buildAdmittanceMatrix(nodes, branches);
  const { deltaP, deltaQ } = calculateMismatch(nodes, Y);
  const maxMismatch = Math.max(...deltaP.map(Math.abs), ...deltaQ.map(Math.abs));

  console.log('功率不平衡量:');
  console.log('ΔP:', deltaP.map(p => p.toFixed(6)));
  console.log('ΔQ:', deltaQ.map(q => q.toFixed(6)));
  console.log(`最大不平衡量: ${maxMismatch.toExponential(3)}`);

  return { nodes, Y, deltaP, deltaQ, maxMismatch };
}

// 测试不平衡负荷系统
function testUnbalancedSystem() {
  console.log('\n=== 测试不平衡负荷系统 ===');

  const nodes = [
    { id: 1, type: 'slack', voltage: 1.0, angle: 0, activePower: 0, reactivePower: 0, name: '平衡节点' },
    { id: 2, type: 'pq', voltage: 1.0, angle: 0, activePower: -0.9, reactivePower: -0.3, name: '重负荷中心' },
    { id: 3, type: 'pq', voltage: 1.0, angle: 0, activePower: -0.1, reactivePower: -0.05, name: '轻负荷节点' }
  ];

  const branches = [
    { from: 1, to: 2, resistance: 0.02, reactance: 0.06, susceptance: 0 },
    { from: 1, to: 3, resistance: 0.03, reactance: 0.09, susceptance: 0 },
    { from: 2, to: 3, resistance: 0.025, reactance: 0.075, susceptance: 0 }
  ];

  const Y = buildAdmittanceMatrix(nodes, branches);
  const { deltaP, deltaQ } = calculateMismatch(nodes, Y);
  const maxMismatch = Math.max(...deltaP.map(Math.abs), ...deltaQ.map(Math.abs));

  console.log('功率不平衡量:');
  console.log('ΔP:', deltaP.map(p => p.toFixed(6)));
  console.log('ΔQ:', deltaQ.map(q => q.toFixed(6)));
  console.log(`最大不平衡量: ${maxMismatch.toExponential(3)}`);

  return { nodes, Y, deltaP, deltaQ, maxMismatch };
}

// 运行所有测试
console.log('开始潮流计算测试...\n');

const basicResult = testBasicSystem();
const heavyResult = testHeavyLoadSystem();
const unbalancedResult = testUnbalancedSystem();

console.log('\n=== 测试总结 ===');
console.log(`基础系统最大不平衡量: ${basicResult.maxMismatch.toExponential(3)}`);
console.log(`重负荷系统最大不平衡量: ${heavyResult.maxMismatch.toExponential(3)}`);
console.log(`不平衡系统最大不平衡量: ${unbalancedResult.maxMismatch.toExponential(3)}`);

console.log('\n导纳矩阵构建和功率计算都正常工作！');
