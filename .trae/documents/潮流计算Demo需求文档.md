## 1. 产品概述

本项目是一个潮流计算学习演示应用，旨在帮助开发者理解电力系统潮流计算的基本原理和算法实现。
- 通过可视化界面展示简单的电力网络拓扑，让用户直观理解潮流计算的输入输出关系。
- 面向有编程基础但缺乏电力系统知识的开发者，提供入门级的潮流计算学习工具。

## 2. 核心功能

### 2.1 用户角色
本应用不区分用户角色，所有访问者都可以直接使用全部功能。

### 2.2 功能模块
我们的潮流计算demo包含以下主要页面：
1. **主页面**：网络拓扑展示、参数输入面板、计算结果显示
2. **算法说明页**：牛顿-拉夫逊算法原理介绍、计算步骤说明
3. **示例库页**：预设的典型电力网络案例、一键加载功能

### 2.3 页面详情

| 页面名称 | 模块名称 | 功能描述 |
|----------|----------|----------|
| 主页面 | 网络拓扑图 | 显示简单的3节点电力系统，包含发电机、负荷、传输线 |
| 主页面 | 参数输入面板 | 输入节点功率、电压幅值、线路阻抗等参数 |
| 主页面 | 计算引擎 | 实现简化的牛顿-拉夫逊潮流计算算法 |
| 主页面 | 结果展示 | 显示各节点电压、功率分布、线路潮流等计算结果 |
| 算法说明页 | 原理介绍 | 图文并茂地解释潮流计算的物理意义和数学模型 |
| 算法说明页 | 算法步骤 | 详细说明牛顿-拉夫逊迭代过程和收敛判据 |
| 示例库页 | 案例列表 | 提供IEEE 3节点、简单环网等典型算例 |
| 示例库页 | 快速加载 | 一键加载预设参数，方便用户快速体验 |

## 3. 核心流程

用户访问主页面，可以直接查看默认的3节点电力系统。用户可以修改节点参数（如负荷功率、发电机出力），然后点击"开始计算"按钮触发潮流计算。系统使用牛顿-拉夫逊算法求解，并在拓扑图上实时更新计算结果。用户还可以访问算法说明页了解计算原理，或在示例库页加载不同的测试案例。

```mermaid
graph TD
  A[主页面] --> B[修改参数]
  B --> C[开始计算]
  C --> D[结果展示]
  A --> E[算法说明页]
  A --> F[示例库页]
  F --> A
```

## 4. 用户界面设计

### 4.1 设计风格
- 主色调：深蓝色(#1e3a8a)和橙色(#f97316)，体现电力行业的专业感
- 按钮样式：圆角矩形，带有轻微阴影效果
- 字体：系统默认字体，标题16px，正文14px
- 布局风格：左侧拓扑图，右侧参数面板的分栏布局
- 图标风格：使用简洁的线性图标表示电力设备

### 4.2 页面设计概览

| 页面名称 | 模块名称 | UI元素 |
|----------|----------|--------|
| 主页面 | 网络拓扑图 | SVG绘制的节点和线路，节点显示编号和电压值，线路显示功率流向 |
| 主页面 | 参数输入面板 | 表格形式的参数输入框，包含节点类型、功率、电压等字段 |
| 主页面 | 计算控制 | 蓝色的"开始计算"按钮，计算状态指示器，迭代次数显示 |
| 算法说明页 | 内容展示 | 卡片式布局，包含公式渲染、流程图、代码示例 |
| 示例库页 | 案例卡片 | 网格布局的案例卡片，每个卡片显示案例名称、节点数、描述 |

### 4.3 响应式设计
采用桌面优先的设计策略，在移动端将左右分栏改为上下布局，确保在小屏幕设备上的可用性。