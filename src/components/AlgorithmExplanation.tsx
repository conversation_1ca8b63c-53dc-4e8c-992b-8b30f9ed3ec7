import React from 'react';
import { <PERSON><PERSON><PERSON>, Calculator, Lightbulb, ArrowRight } from 'lucide-react';

const AlgorithmExplanation: React.FC = () => {
  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6">
      <div className="flex items-center gap-2 mb-6">
        <BookOpen className="w-5 h-5 text-blue-600" />
        <h3 className="text-lg font-semibold text-gray-800">牛顿-拉夫逊潮流计算算法</h3>
      </div>

      <div className="space-y-8">
        {/* 算法概述 */}
        <section>
          <div className="flex items-center gap-2 mb-4">
            <Lightbulb className="w-4 h-4 text-yellow-500" />
            <h4 className="text-md font-semibold text-gray-800">算法概述</h4>
          </div>
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <p className="text-gray-700 leading-relaxed">
              牛顿-拉夫逊法是电力系统潮流计算中最常用的数值方法。它通过迭代求解非线性方程组，
              计算电力网络中各节点的电压幅值和相角，以及各支路的功率分布。
            </p>
          </div>
        </section>

        {/* 数学原理 */}
        <section>
          <h4 className="text-md font-semibold text-gray-800 mb-4">数学原理</h4>
          <div className="grid md:grid-cols-2 gap-6">
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <h5 className="font-medium text-gray-800 mb-3">功率方程</h5>
              <div className="space-y-2 text-sm">
                <div className="bg-white p-3 rounded border font-mono">
                  P<sub>i</sub> = V<sub>i</sub> ∑ V<sub>j</sub>(G<sub>ij</sub>cos θ<sub>ij</sub> + B<sub>ij</sub>sin θ<sub>ij</sub>)
                </div>
                <div className="bg-white p-3 rounded border font-mono">
                  Q<sub>i</sub> = V<sub>i</sub> ∑ V<sub>j</sub>(G<sub>ij</sub>sin θ<sub>ij</sub> - B<sub>ij</sub>cos θ<sub>ij</sub>)
                </div>
                <p className="text-gray-600 text-xs mt-2">
                  其中：P为有功功率，Q为无功功率，V为电压幅值，θ为电压相角差，G和B为导纳矩阵元素
                </p>
              </div>
            </div>

            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <h5 className="font-medium text-gray-800 mb-3">雅可比矩阵</h5>
              <div className="space-y-2 text-sm">
                <div className="bg-white p-3 rounded border font-mono text-center">
                  J = [∂P/∂θ  ∂P/∂V]<br/>
                  &nbsp;&nbsp;&nbsp;&nbsp;[∂Q/∂θ  ∂Q/∂V]
                </div>
                <p className="text-gray-600 text-xs mt-2">
                  雅可比矩阵包含功率对电压幅值和相角的偏导数，用于线性化非线性方程组
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* 算法步骤 */}
        <section>
          <div className="flex items-center gap-2 mb-4">
            <Calculator className="w-4 h-4 text-green-500" />
            <h4 className="text-md font-semibold text-gray-800">算法步骤</h4>
          </div>
          <div className="space-y-4">
            {[
              {
                step: 1,
                title: "初始化",
                description: "设置各节点电压初值，通常为1.0∠0°（除平衡节点外）",
                details: "平衡节点电压幅值和相角固定，PV节点电压幅值给定，PQ节点功率给定"
              },
              {
                step: 2,
                title: "构建导纳矩阵",
                description: "根据网络拓扑和线路参数构建节点导纳矩阵Y",
                details: "Y矩阵反映了网络的电气连接关系，是潮流计算的基础"
              },
              {
                step: 3,
                title: "计算功率不平衡量",
                description: "计算各节点的功率不平衡量ΔP和ΔQ",
                details: "ΔP = P指定 - P计算，ΔQ = Q指定 - Q计算"
              },
              {
                step: 4,
                title: "检查收敛性",
                description: "判断最大功率不平衡量是否小于收敛精度",
                details: "通常收敛精度设为1×10⁻⁶，如果收敛则结束计算"
              },
              {
                step: 5,
                title: "构建雅可比矩阵",
                description: "计算功率方程对电压变量的偏导数矩阵",
                details: "雅可比矩阵用于线性化非线性功率方程"
              },
              {
                step: 6,
                title: "求解修正方程",
                description: "求解线性方程组J·Δx = Δf，得到电压修正量",
                details: "使用LU分解或高斯消元法求解线性方程组"
              },
              {
                step: 7,
                title: "更新电压",
                description: "用修正量更新各节点电压幅值和相角",
                details: "θ新 = θ旧 + Δθ，V新 = V旧 + ΔV"
              },
              {
                step: 8,
                title: "返回步骤3",
                description: "继续下一次迭代直到收敛或达到最大迭代次数",
                details: "通常最大迭代次数设为20-50次"
              }
            ].map((item, index) => (
              <div key={index} className="flex gap-4">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                    {item.step}
                  </div>
                </div>
                <div className="flex-1">
                  <div className="bg-white border border-gray-200 rounded-lg p-4">
                    <h5 className="font-medium text-gray-800 mb-2">{item.title}</h5>
                    <p className="text-gray-700 text-sm mb-2">{item.description}</p>
                    <p className="text-gray-600 text-xs">{item.details}</p>
                  </div>
                </div>
                {index < 7 && (
                  <div className="flex-shrink-0 flex items-center">
                    <ArrowRight className="w-4 h-4 text-gray-400" />
                  </div>
                )}
              </div>
            ))}
          </div>
        </section>

        {/* 收敛特性 */}
        <section>
          <h4 className="text-md font-semibold text-gray-800 mb-4">收敛特性</h4>
          <div className="grid md:grid-cols-3 gap-4">
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <h5 className="font-medium text-green-800 mb-2">优点</h5>
              <ul className="text-sm text-green-700 space-y-1">
                <li>• 收敛速度快（二次收敛）</li>
                <li>• 适用于大规模系统</li>
                <li>• 数值稳定性好</li>
                <li>• 工程应用广泛</li>
              </ul>
            </div>
            
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h5 className="font-medium text-yellow-800 mb-2">缺点</h5>
              <ul className="text-sm text-yellow-700 space-y-1">
                <li>• 需要计算雅可比矩阵</li>
                <li>• 对初值敏感</li>
                <li>• 可能不收敛</li>
                <li>• 计算量较大</li>
              </ul>
            </div>
            
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h5 className="font-medium text-blue-800 mb-2">应用场景</h5>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• 电力系统规划</li>
                <li>• 运行状态分析</li>
                <li>• 故障分析</li>
                <li>• 优化计算</li>
              </ul>
            </div>
          </div>
        </section>

        {/* 实现要点 */}
        <section>
          <h4 className="text-md font-semibold text-gray-800 mb-4">JavaScript实现要点</h4>
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <div className="space-y-4">
              <div>
                <h5 className="font-medium text-gray-800 mb-2">1. 复数运算</h5>
                <div className="bg-white p-3 rounded border font-mono text-sm">
                  <code className="text-blue-600">class Complex &#123;<br/>
                  &nbsp;&nbsp;constructor(real, imag) &#123; ... &#125;<br/>
                  &nbsp;&nbsp;multiply(other) &#123; ... &#125;<br/>
                  &nbsp;&nbsp;magnitude() &#123; ... &#125;<br/>
                  &#125;</code>
                </div>
              </div>
              
              <div>
                <h5 className="font-medium text-gray-800 mb-2">2. 矩阵运算</h5>
                <div className="bg-white p-3 rounded border font-mono text-sm">
                  <code className="text-blue-600">function buildAdmittanceMatrix(nodes, branches) &#123;<br/>
                  &nbsp;&nbsp;// 构建节点导纳矩阵<br/>
                  &nbsp;&nbsp;const Y = Array(n).fill(null).map(() =&gt; Array(n).fill(0));<br/>
                  &#125;</code>
                </div>
              </div>
              
              <div>
                <h5 className="font-medium text-gray-800 mb-2">3. 迭代控制</h5>
                <div className="bg-white p-3 rounded border font-mono text-sm">
                  <code className="text-blue-600">while (iteration &lt; maxIterations && !converged) &#123;<br/>
                  &nbsp;&nbsp;// 计算不平衡量<br/>
                  &nbsp;&nbsp;// 检查收敛性<br/>
                  &nbsp;&nbsp;// 更新电压<br/>
                  &#125;</code>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* 学习建议 */}
        <section>
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-6">
            <h4 className="text-md font-semibold text-gray-800 mb-4">学习建议</h4>
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <h5 className="font-medium text-gray-800 mb-2">理论基础</h5>
                <ul className="text-sm text-gray-700 space-y-1">
                  <li>• 学习电路基础和复数运算</li>
                  <li>• 理解电力系统基本概念</li>
                  <li>• 掌握数值分析方法</li>
                  <li>• 了解线性代数知识</li>
                </ul>
              </div>
              <div>
                <h5 className="font-medium text-gray-800 mb-2">实践建议</h5>
                <ul className="text-sm text-gray-700 space-y-1">
                  <li>• 从简单系统开始练习</li>
                  <li>• 观察参数变化对结果的影响</li>
                  <li>• 分析收敛过程和特性</li>
                  <li>• 尝试不同的初值设置</li>
                </ul>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
};

export default AlgorithmExplanation;