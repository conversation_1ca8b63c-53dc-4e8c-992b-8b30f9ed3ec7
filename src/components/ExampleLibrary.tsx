import React from 'react';
import { Node, Branch } from '../utils/powerFlow';
import { Library, Play, Info } from 'lucide-react';

interface ExampleLibraryProps {
  onLoadExample: (nodes: Node[], branches: Branch[]) => void;
}

interface Example {
  id: string;
  name: string;
  description: string;
  difficulty: 'easy' | 'medium' | 'hard';
  nodes: Node[];
  branches: Branch[];
  features: string[];
}

const ExampleLibrary: React.FC<ExampleLibraryProps> = ({ onLoadExample }) => {
  const examples: Example[] = [
    {
      id: 'default',
      name: '基础3节点系统',
      description: '最简单的3节点放射状网络，适合初学者理解潮流计算基本概念',
      difficulty: 'easy',
      features: ['1个平衡节点', '2个PQ负荷节点', '放射状网络', '轻负荷'],
      nodes: [
        { id: 1, type: 'slack', voltage: 1.0, angle: 0, activePower: 0, reactivePower: 0, name: '平衡节点' },
        { id: 2, type: 'pq', voltage: 1.0, angle: 0, activePower: -0.5, reactivePower: -0.2, name: '负荷节点1' },
        { id: 3, type: 'pq', voltage: 1.0, angle: 0, activePower: -0.3, reactivePower: -0.1, name: '负荷节点2' }
      ],
      branches: [
        { from: 1, to: 2, resistance: 0.02, reactance: 0.06, susceptance: 0 },
        { from: 1, to: 3, resistance: 0.03, reactance: 0.09, susceptance: 0 },
        { from: 2, to: 3, resistance: 0.025, reactance: 0.075, susceptance: 0 }
      ]
    },
    {
      id: 'heavy_load',
      name: '重负荷系统',
      description: '相同网络拓扑但负荷较重，观察重负荷对电压水平的影响',
      difficulty: 'medium',
      features: ['重负荷场景', '电压偏低', '更多迭代次数', '收敛性挑战'],
      nodes: [
        { id: 1, type: 'slack', voltage: 1.0, angle: 0, activePower: 0, reactivePower: 0, name: '平衡节点' },
        { id: 2, type: 'pq', voltage: 1.0, angle: 0, activePower: -0.8, reactivePower: -0.4, name: '重负荷节点1' },
        { id: 3, type: 'pq', voltage: 1.0, angle: 0, activePower: -0.6, reactivePower: -0.3, name: '重负荷节点2' }
      ],
      branches: [
        { from: 1, to: 2, resistance: 0.02, reactance: 0.06, susceptance: 0 },
        { from: 1, to: 3, resistance: 0.03, reactance: 0.09, susceptance: 0 },
        { from: 2, to: 3, resistance: 0.025, reactance: 0.075, susceptance: 0 }
      ]
    },
    {
      id: 'with_generator',
      name: 'PV发电机系统',
      description: '包含PV发电机节点的系统，展示不同节点类型的特点',
      difficulty: 'medium',
      features: ['PV发电机节点', '混合节点类型', '功率平衡', '电压控制'],
      nodes: [
        { id: 1, type: 'slack', voltage: 1.0, angle: 0, activePower: 0, reactivePower: 0, name: '平衡节点' },
        { id: 2, type: 'pv', voltage: 1.02, angle: 0, activePower: 0.4, reactivePower: 0, name: 'PV发电机' },
        { id: 3, type: 'pq', voltage: 1.0, angle: 0, activePower: -0.6, reactivePower: -0.2, name: '负荷节点' }
      ],
      branches: [
        { from: 1, to: 2, resistance: 0.015, reactance: 0.045, susceptance: 0 },
        { from: 1, to: 3, resistance: 0.025, reactance: 0.075, susceptance: 0 },
        { from: 2, to: 3, resistance: 0.02, reactance: 0.06, susceptance: 0 }
      ]
    },
    {
      id: 'high_impedance',
      name: '高阻抗线路系统',
      description: '线路阻抗较大的系统，观察线路参数对潮流分布的影响',
      difficulty: 'medium',
      features: ['高线路阻抗', '电压降较大', '功率损耗明显', '网络约束'],
      nodes: [
        { id: 1, type: 'slack', voltage: 1.0, angle: 0, activePower: 0, reactivePower: 0, name: '平衡节点' },
        { id: 2, type: 'pq', voltage: 1.0, angle: 0, activePower: -0.4, reactivePower: -0.15, name: '负荷节点1' },
        { id: 3, type: 'pq', voltage: 1.0, angle: 0, activePower: -0.35, reactivePower: -0.12, name: '负荷节点2' }
      ],
      branches: [
        { from: 1, to: 2, resistance: 0.08, reactance: 0.15, susceptance: 0 },
        { from: 1, to: 3, resistance: 0.1, reactance: 0.18, susceptance: 0 },
        { from: 2, to: 3, resistance: 0.06, reactance: 0.12, susceptance: 0 }
      ]
    },
    {
      id: 'unbalanced',
      name: '不平衡负荷系统',
      description: '负荷分布不均匀的系统，一个节点负荷很重，另一个很轻',
      difficulty: 'easy',
      features: ['负荷不平衡', '电压差异', '功率分布不均', '实际场景模拟'],
      nodes: [
        { id: 1, type: 'slack', voltage: 1.0, angle: 0, activePower: 0, reactivePower: 0, name: '平衡节点' },
        { id: 2, type: 'pq', voltage: 1.0, angle: 0, activePower: -0.9, reactivePower: -0.3, name: '重负荷中心' },
        { id: 3, type: 'pq', voltage: 1.0, angle: 0, activePower: -0.1, reactivePower: -0.05, name: '轻负荷节点' }
      ],
      branches: [
        { from: 1, to: 2, resistance: 0.02, reactance: 0.06, susceptance: 0 },
        { from: 1, to: 3, resistance: 0.03, reactance: 0.09, susceptance: 0 },
        { from: 2, to: 3, resistance: 0.025, reactance: 0.075, susceptance: 0 }
      ]
    }
  ];

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'bg-green-100 text-green-800 border-green-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'hard': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getDifficultyText = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return '简单';
      case 'medium': return '中等';
      case 'hard': return '困难';
      default: return '未知';
    }
  };

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6">
      <div className="flex items-center gap-2 mb-6">
        <Library className="w-5 h-5 text-blue-600" />
        <h3 className="text-lg font-semibold text-gray-800">示例库</h3>
      </div>

      <div className="grid gap-6">
        {examples.map((example) => (
          <div key={example.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
            <div className="flex justify-between items-start mb-3">
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-2">
                  <h4 className="text-md font-semibold text-gray-800">{example.name}</h4>
                  <span className={`px-2 py-1 text-xs font-medium rounded border ${
                    getDifficultyColor(example.difficulty)
                  }`}>
                    {getDifficultyText(example.difficulty)}
                  </span>
                </div>
                <p className="text-sm text-gray-600 mb-3">{example.description}</p>
              </div>
              <button
                onClick={() => onLoadExample(example.nodes, example.branches)}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
              >
                <Play className="w-4 h-4" />
                加载案例
              </button>
            </div>

            {/* 特性标签 */}
            <div className="flex flex-wrap gap-2 mb-4">
              {example.features.map((feature, index) => (
                <span
                  key={index}
                  className="px-2 py-1 bg-blue-50 text-blue-700 text-xs rounded border border-blue-200"
                >
                  {feature}
                </span>
              ))}
            </div>

            {/* 系统参数预览 */}
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <h5 className="text-sm font-medium text-gray-700 mb-2 flex items-center gap-1">
                  <Info className="w-3 h-3" />
                  节点信息
                </h5>
                <div className="space-y-1">
                  {example.nodes.map((node) => (
                    <div key={node.id} className="flex justify-between text-xs">
                      <span className="text-gray-600">
                        节点{node.id} ({node.type === 'slack' ? '平衡' : node.type === 'pv' ? 'PV' : 'PQ'})
                      </span>
                      <span className="text-gray-800 font-mono">
                        {node.type === 'slack' ? 'V=1.0∠0°' : 
                         node.type === 'pv' ? `P=${node.activePower.toFixed(2)}, V=${node.voltage.toFixed(2)}` :
                         `P=${node.activePower.toFixed(2)}, Q=${node.reactivePower.toFixed(2)}`}
                      </span>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <h5 className="text-sm font-medium text-gray-700 mb-2 flex items-center gap-1">
                  <Info className="w-3 h-3" />
                  线路信息
                </h5>
                <div className="space-y-1">
                  {example.branches.map((branch, index) => {
                    const impedance = Math.sqrt(branch.resistance ** 2 + branch.reactance ** 2);
                    return (
                      <div key={index} className="flex justify-between text-xs">
                        <span className="text-gray-600">
                          线路{branch.from}-{branch.to}
                        </span>
                        <span className="text-gray-800 font-mono">
                          Z={impedance.toFixed(3)} p.u.
                        </span>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>

            {/* 学习要点 */}
            <div className="mt-4 p-3 bg-gray-50 rounded border">
              <h5 className="text-sm font-medium text-gray-700 mb-2">学习要点</h5>
              <p className="text-xs text-gray-600">
                {example.id === 'default' && '理解基本的潮流计算过程，观察迭代收敛特性'}
                {example.id === 'heavy_load' && '观察重负荷对系统电压水平的影响，理解电压稳定性'}
                {example.id === 'with_generator' && '学习PV节点的特点，理解不同节点类型的约束条件'}
                {example.id === 'high_impedance' && '分析线路阻抗对功率传输和电压降的影响'}
                {example.id === 'unbalanced' && '观察负荷不平衡对系统运行状态的影响'}
              </p>
            </div>
          </div>
        ))}
      </div>

      {/* 使用说明 */}
      <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h4 className="font-medium text-blue-800 mb-2">使用说明</h4>
        <div className="text-sm text-blue-700 space-y-1">
          <p>• 点击"加载案例"按钮将示例参数加载到主界面</p>
          <p>• 加载后可以修改参数，观察不同设置对计算结果的影响</p>
          <p>• 建议按难度从简单到复杂逐步学习</p>
          <p>• 每个案例都有特定的学习目标，注意观察相应的现象</p>
        </div>
      </div>
    </div>
  );
};

export default ExampleLibrary;