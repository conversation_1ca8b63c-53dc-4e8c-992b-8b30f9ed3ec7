import React from 'react';
import { Node, Branch } from '../utils/powerFlow';

interface NetworkTopologyProps {
  nodes: Node[];
  branches: Branch[];
  width?: number;
  height?: number;
}

const NetworkTopology: React.FC<NetworkTopologyProps> = ({ 
  nodes, 
  branches, 
  width = 600, 
  height = 400 
}) => {
  // 节点位置（固定布局）
  const nodePositions = {
    1: { x: 100, y: 200 }, // 平衡节点
    2: { x: 350, y: 100 }, // 负荷节点1
    3: { x: 350, y: 300 }  // 负荷节点2
  };

  // 获取节点颜色
  const getNodeColor = (nodeType: string) => {
    switch (nodeType) {
      case 'slack': return '#ef4444'; // 红色 - 平衡节点
      case 'pv': return '#22c55e';    // 绿色 - PV节点
      case 'pq': return '#3b82f6';    // 蓝色 - PQ节点
      default: return '#6b7280';      // 灰色
    }
  };

  // 获取节点类型中文名称
  const getNodeTypeName = (nodeType: string) => {
    switch (nodeType) {
      case 'slack': return '平衡';
      case 'pv': return 'PV';
      case 'pq': return 'PQ';
      default: return '未知';
    }
  };

  // 计算线路功率流向箭头
  const calculateArrowPosition = (from: any, to: any, offset: number = 0.7) => {
    const dx = to.x - from.x;
    const dy = to.y - from.y;
    const length = Math.sqrt(dx * dx + dy * dy);
    
    return {
      x: from.x + dx * offset,
      y: from.y + dy * offset,
      angle: Math.atan2(dy, dx) * 180 / Math.PI
    };
  };

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4">
      <h3 className="text-lg font-semibold mb-4 text-gray-800">电力系统网络拓扑</h3>
      
      <svg width={width} height={height} className="border border-gray-100 rounded">
        {/* 网格背景 */}
        <defs>
          <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
            <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#f3f4f6" strokeWidth="1"/>
          </pattern>
          
          {/* 箭头标记 */}
          <marker id="arrowhead" markerWidth="10" markerHeight="7" 
                  refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#6b7280" />
          </marker>
        </defs>
        
        <rect width="100%" height="100%" fill="url(#grid)" />
        
        {/* 绘制线路 */}
        {branches.map((branch, index) => {
          const fromPos = nodePositions[branch.from as keyof typeof nodePositions];
          const toPos = nodePositions[branch.to as keyof typeof nodePositions];
          
          if (!fromPos || !toPos) return null;
          
          const midX = (fromPos.x + toPos.x) / 2;
          const midY = (fromPos.y + toPos.y) / 2;
          const impedance = Math.sqrt(branch.resistance ** 2 + branch.reactance ** 2);
          
          return (
            <g key={index}>
              {/* 线路 */}
              <line
                x1={fromPos.x}
                y1={fromPos.y}
                x2={toPos.x}
                y2={toPos.y}
                stroke="#374151"
                strokeWidth="2"
                markerEnd="url(#arrowhead)"
              />
              
              {/* 线路参数标签 */}
              <rect
                x={midX - 25}
                y={midY - 10}
                width="50"
                height="20"
                fill="white"
                stroke="#d1d5db"
                strokeWidth="1"
                rx="3"
              />
              <text
                x={midX}
                y={midY + 4}
                textAnchor="middle"
                fontSize="10"
                fill="#374151"
              >
                Z={impedance.toFixed(3)}
              </text>
            </g>
          );
        })}
        
        {/* 绘制节点 */}
        {nodes.map((node) => {
          const pos = nodePositions[node.id as keyof typeof nodePositions];
          if (!pos) return null;
          
          return (
            <g key={node.id}>
              {/* 节点圆圈 */}
              <circle
                cx={pos.x}
                cy={pos.y}
                r="25"
                fill={getNodeColor(node.type)}
                stroke="white"
                strokeWidth="3"
                className="drop-shadow-md"
              />
              
              {/* 节点编号 */}
              <text
                x={pos.x}
                y={pos.y + 5}
                textAnchor="middle"
                fontSize="14"
                fontWeight="bold"
                fill="white"
              >
                {node.id}
              </text>
              
              {/* 节点信息标签 */}
              <g>
                <rect
                  x={pos.x - 40}
                  y={pos.y - 60}
                  width="80"
                  height="25"
                  fill="white"
                  stroke={getNodeColor(node.type)}
                  strokeWidth="2"
                  rx="4"
                  className="drop-shadow-sm"
                />
                <text
                  x={pos.x}
                  y={pos.y - 45}
                  textAnchor="middle"
                  fontSize="10"
                  fontWeight="bold"
                  fill={getNodeColor(node.type)}
                >
                  {getNodeTypeName(node.type)}节点
                </text>
                <text
                  x={pos.x}
                  y={pos.y - 35}
                  textAnchor="middle"
                  fontSize="9"
                  fill="#374151"
                >
                  V={node.voltage.toFixed(3)}∠{(node.angle * 180 / Math.PI).toFixed(1)}°
                </text>
              </g>
              
              {/* 功率信息 */}
              {(node.activePower !== 0 || node.reactivePower !== 0) && (
                <g>
                  <rect
                    x={pos.x - 35}
                    y={pos.y + 35}
                    width="70"
                    height="20"
                    fill="#f9fafb"
                    stroke="#d1d5db"
                    strokeWidth="1"
                    rx="3"
                  />
                  <text
                    x={pos.x}
                    y={pos.y + 48}
                    textAnchor="middle"
                    fontSize="9"
                    fill="#374151"
                  >
                    P={node.activePower.toFixed(2)} Q={node.reactivePower.toFixed(2)}
                  </text>
                </g>
              )}
            </g>
          );
        })}
        
        {/* 图例 */}
        <g transform="translate(450, 20)">
          <rect x="0" y="0" width="140" height="80" fill="white" stroke="#d1d5db" strokeWidth="1" rx="4" />
          <text x="70" y="15" textAnchor="middle" fontSize="12" fontWeight="bold" fill="#374151">图例</text>
          
          <circle cx="15" cy="30" r="8" fill="#ef4444" />
          <text x="30" y="35" fontSize="10" fill="#374151">平衡节点</text>
          
          <circle cx="15" cy="50" r="8" fill="#3b82f6" />
          <text x="30" y="55" fontSize="10" fill="#374151">PQ节点</text>
          
          <line x1="15" y1="65" x2="35" y2="65" stroke="#374151" strokeWidth="2" markerEnd="url(#arrowhead)" />
          <text x="45" y="70" fontSize="10" fill="#374151">传输线</text>
        </g>
      </svg>
      
      <div className="mt-4 text-sm text-gray-600">
        <p><strong>说明：</strong></p>
        <ul className="list-disc list-inside space-y-1">
          <li>红色圆圈：平衡节点（电压幅值和相角固定）</li>
          <li>蓝色圆圈：PQ节点（有功和无功功率给定）</li>
          <li>箭头线路：表示功率传输方向</li>
          <li>V表示电压幅值（标幺值），角度表示电压相角</li>
          <li>P表示有功功率，Q表示无功功率（标幺值）</li>
        </ul>
      </div>
    </div>
  );
};

export default NetworkTopology;