import React from 'react';
import { PowerFlowResult } from '../utils/powerFlow';
import { CheckCircle, XCircle, BarChart3, TrendingUp } from 'lucide-react';

interface ResultsPanelProps {
  result: PowerFlowResult | null;
}

const ResultsPanel: React.FC<ResultsPanelProps> = ({ result }) => {
  if (!result) {
    return (
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <div className="flex items-center gap-2 mb-4">
          <BarChart3 className="w-5 h-5 text-gray-400" />
          <h3 className="text-lg font-semibold text-gray-800">计算结果</h3>
        </div>
        <div className="text-center py-8 text-gray-500">
          <BarChart3 className="w-12 h-12 mx-auto mb-3 text-gray-300" />
          <p>请点击"开始潮流计算"查看结果</p>
        </div>
      </div>
    );
  }

  const { nodes, iterations, converged, maxMismatch, iterationHistory } = result;

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6">
      <div className="flex items-center gap-2 mb-6">
        <BarChart3 className="w-5 h-5 text-blue-600" />
        <h3 className="text-lg font-semibold text-gray-800">计算结果</h3>
      </div>

      {/* 收敛状态 */}
      <div className="mb-6">
        <div className={`flex items-center gap-2 p-3 rounded-lg ${
          converged ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
        }`}>
          {converged ? (
            <CheckCircle className="w-5 h-5 text-green-600" />
          ) : (
            <XCircle className="w-5 h-5 text-red-600" />
          )}
          <div>
            <p className={`font-medium ${
              converged ? 'text-green-800' : 'text-red-800'
            }`}>
              {converged ? '计算收敛成功' : '计算未收敛'}
            </p>
            <p className={`text-sm ${
              converged ? 'text-green-600' : 'text-red-600'
            }`}>
              迭代次数: {iterations} | 最大不平衡量: {maxMismatch.toExponential(3)}
            </p>
          </div>
        </div>
      </div>

      {/* 节点结果表格 */}
      <div className="mb-6">
        <h4 className="font-medium text-gray-800 mb-3">节点电压结果</h4>
        <div className="overflow-x-auto">
          <table className="w-full border-collapse border border-gray-200 text-sm">
            <thead>
              <tr className="bg-gray-50">
                <th className="border border-gray-200 px-3 py-2 text-left font-medium text-gray-700">
                  节点
                </th>
                <th className="border border-gray-200 px-3 py-2 text-left font-medium text-gray-700">
                  类型
                </th>
                <th className="border border-gray-200 px-3 py-2 text-left font-medium text-gray-700">
                  电压幅值(p.u.)
                </th>
                <th className="border border-gray-200 px-3 py-2 text-left font-medium text-gray-700">
                  电压相角(度)
                </th>
                <th className="border border-gray-200 px-3 py-2 text-left font-medium text-gray-700">
                  有功功率(p.u.)
                </th>
                <th className="border border-gray-200 px-3 py-2 text-left font-medium text-gray-700">
                  无功功率(p.u.)
                </th>
              </tr>
            </thead>
            <tbody>
              {nodes.map((node) => {
                const getNodeTypeName = (type: string) => {
                  switch (type) {
                    case 'slack': return '平衡';
                    case 'pv': return 'PV';
                    case 'pq': return 'PQ';
                    default: return '未知';
                  }
                };

                const getVoltageStatus = (voltage: number) => {
                  if (voltage < 0.95) return 'text-red-600 bg-red-50';
                  if (voltage > 1.05) return 'text-orange-600 bg-orange-50';
                  return 'text-green-600 bg-green-50';
                };

                return (
                  <tr key={node.id} className="hover:bg-gray-50">
                    <td className="border border-gray-200 px-3 py-2">
                      <div className="flex items-center gap-2">
                        <div className={`w-3 h-3 rounded-full ${
                          node.type === 'slack' ? 'bg-red-500' :
                          node.type === 'pv' ? 'bg-green-500' : 'bg-blue-500'
                        }`} />
                        <span className="font-medium">{node.id}</span>
                      </div>
                    </td>
                    <td className="border border-gray-200 px-3 py-2">
                      <span className="text-xs px-2 py-1 rounded bg-gray-100 text-gray-700">
                        {getNodeTypeName(node.type)}
                      </span>
                    </td>
                    <td className="border border-gray-200 px-3 py-2">
                      <span className={`px-2 py-1 rounded text-xs font-medium ${
                        getVoltageStatus(node.voltage)
                      }`}>
                        {node.voltage.toFixed(4)}
                      </span>
                    </td>
                    <td className="border border-gray-200 px-3 py-2 font-mono">
                      {(node.angle * 180 / Math.PI).toFixed(2)}°
                    </td>
                    <td className="border border-gray-200 px-3 py-2 font-mono">
                      {node.activePower.toFixed(4)}
                    </td>
                    <td className="border border-gray-200 px-3 py-2 font-mono">
                      {node.reactivePower.toFixed(4)}
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>

      {/* 迭代过程 */}
      {iterationHistory.length > 0 && (
        <div className="mb-6">
          <div className="flex items-center gap-2 mb-3">
            <TrendingUp className="w-4 h-4 text-blue-600" />
            <h4 className="font-medium text-gray-800">迭代收敛过程</h4>
          </div>
          
          {/* 收敛曲线图 */}
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-4">
            <div className="relative h-32">
              <svg width="100%" height="100%" className="overflow-visible">
                <defs>
                  <linearGradient id="convergenceGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                    <stop offset="0%" stopColor="#3b82f6" stopOpacity="0.3" />
                    <stop offset="100%" stopColor="#3b82f6" stopOpacity="0.1" />
                  </linearGradient>
                </defs>
                
                {/* 绘制收敛曲线 */}
                {iterationHistory.length > 1 && (
                  <g>
                    {/* 背景网格 */}
                    {[...Array(5)].map((_, i) => (
                      <line
                        key={i}
                        x1="0"
                        y1={i * 25}
                        x2="100%"
                        y2={i * 25}
                        stroke="#e5e7eb"
                        strokeWidth="1"
                        strokeDasharray="2,2"
                      />
                    ))}
                    
                    {/* 数据线 */}
                    <polyline
                      points={iterationHistory.map((data, index) => {
                        const x = (index / (iterationHistory.length - 1)) * 100;
                        const y = 100 - (Math.log10(data.maxMismatch + 1e-10) + 10) * 10;
                        return `${x}%,${Math.max(0, Math.min(100, y))}`;
                      }).join(' ')}
                      fill="none"
                      stroke="#3b82f6"
                      strokeWidth="2"
                    />
                    
                    {/* 数据点 */}
                    {iterationHistory.map((data, index) => {
                      const x = (index / (iterationHistory.length - 1)) * 100;
                      const y = 100 - (Math.log10(data.maxMismatch + 1e-10) + 10) * 10;
                      return (
                        <circle
                          key={index}
                          cx={`${x}%`}
                          cy={Math.max(0, Math.min(100, y))}
                          r="3"
                          fill="#3b82f6"
                          stroke="white"
                          strokeWidth="2"
                        />
                      );
                    })}
                  </g>
                )}
                
                {/* 坐标轴标签 */}
                <text x="50%" y="115" textAnchor="middle" fontSize="12" fill="#6b7280">
                  迭代次数
                </text>
                <text x="-10" y="50%" textAnchor="middle" fontSize="12" fill="#6b7280" transform="rotate(-90, -10, 50)">
                  不平衡量(log)
                </text>
              </svg>
            </div>
          </div>
          
          {/* 迭代详情表格 */}
          <div className="max-h-48 overflow-y-auto">
            <table className="w-full border-collapse border border-gray-200 text-xs">
              <thead className="sticky top-0 bg-white">
                <tr className="bg-gray-50">
                  <th className="border border-gray-200 px-2 py-1 text-left font-medium text-gray-700">
                    迭代
                  </th>
                  <th className="border border-gray-200 px-2 py-1 text-left font-medium text-gray-700">
                    最大不平衡量
                  </th>
                  <th className="border border-gray-200 px-2 py-1 text-left font-medium text-gray-700">
                    节点1电压
                  </th>
                  <th className="border border-gray-200 px-2 py-1 text-left font-medium text-gray-700">
                    节点2电压
                  </th>
                  <th className="border border-gray-200 px-2 py-1 text-left font-medium text-gray-700">
                    节点3电压
                  </th>
                </tr>
              </thead>
              <tbody>
                {iterationHistory.map((data) => (
                  <tr key={data.iteration} className="hover:bg-gray-50">
                    <td className="border border-gray-200 px-2 py-1 font-medium">
                      {data.iteration}
                    </td>
                    <td className="border border-gray-200 px-2 py-1 font-mono">
                      {data.maxMismatch.toExponential(2)}
                    </td>
                    {data.voltages.map((voltage) => (
                      <td key={voltage.id} className="border border-gray-200 px-2 py-1 font-mono">
                        {voltage.voltage.toFixed(4)}∠{(voltage.angle * 180 / Math.PI).toFixed(1)}°
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* 结果分析 */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="font-medium text-blue-800 mb-2">结果分析</h4>
        <div className="text-sm text-blue-700 space-y-1">
          {converged ? (
            <>
              <p>✓ 潮流计算成功收敛，系统运行状态稳定</p>
              <p>✓ 迭代{iterations}次达到收敛精度要求</p>
              {nodes.some(n => n.voltage < 0.95 || n.voltage > 1.05) ? (
                <p>⚠️ 部分节点电压超出正常范围(0.95-1.05 p.u.)，需要调整</p>
              ) : (
                <p>✓ 所有节点电压均在正常范围内</p>
              )}
            </>
          ) : (
            <>
              <p>✗ 潮流计算未收敛，可能原因：</p>
              <p>• 系统参数设置不合理</p>
              <p>• 负荷过重导致电压崩溃</p>
              <p>• 网络拓扑存在问题</p>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default ResultsPanel;