import React from 'react';
import { Node } from '../utils/powerFlow';
import { Settings, Zap, Power } from 'lucide-react';

interface ParameterPanelProps {
  nodes: Node[];
  onNodeChange: (nodeId: number, field: keyof Node, value: number) => void;
  onCalculate: () => void;
  isCalculating: boolean;
}

const ParameterPanel: React.FC<ParameterPanelProps> = ({
  nodes,
  onNodeChange,
  onCalculate,
  isCalculating
}) => {
  const handleInputChange = (nodeId: number, field: keyof Node, value: string) => {
    const numValue = parseFloat(value) || 0;
    onNodeChange(nodeId, field, numValue);
  };

  const getNodeTypeOptions = () => [
    { value: 'slack', label: '平衡节点 (Slack)' },
    { value: 'pv', label: 'PV节点 (发电机)' },
    { value: 'pq', label: 'PQ节点 (负荷)' }
  ];

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6">
      <div className="flex items-center gap-2 mb-6">
        <Settings className="w-5 h-5 text-blue-600" />
        <h3 className="text-lg font-semibold text-gray-800">参数设置</h3>
      </div>

      <div className="space-y-6">
        {/* 节点参数表格 */}
        <div className="overflow-x-auto">
          <table className="w-full border-collapse border border-gray-200 text-sm">
            <thead>
              <tr className="bg-gray-50">
                <th className="border border-gray-200 px-3 py-2 text-left font-medium text-gray-700">
                  节点
                </th>
                <th className="border border-gray-200 px-3 py-2 text-left font-medium text-gray-700">
                  类型
                </th>
                <th className="border border-gray-200 px-3 py-2 text-left font-medium text-gray-700">
                  电压幅值(p.u.)
                </th>
                <th className="border border-gray-200 px-3 py-2 text-left font-medium text-gray-700">
                  有功功率(p.u.)
                </th>
                <th className="border border-gray-200 px-3 py-2 text-left font-medium text-gray-700">
                  无功功率(p.u.)
                </th>
              </tr>
            </thead>
            <tbody>
              {nodes.map((node) => (
                <tr key={node.id} className="hover:bg-gray-50">
                  <td className="border border-gray-200 px-3 py-2">
                    <div className="flex items-center gap-2">
                      <div className={`w-3 h-3 rounded-full ${
                        node.type === 'slack' ? 'bg-red-500' :
                        node.type === 'pv' ? 'bg-green-500' : 'bg-blue-500'
                      }`} />
                      <span className="font-medium">{node.id}</span>
                    </div>
                  </td>
                  <td className="border border-gray-200 px-3 py-2">
                    <select
                      value={node.type}
                      onChange={(e) => onNodeChange(node.id, 'type', e.target.value as any)}
                      className="w-full px-2 py-1 border border-gray-300 rounded text-xs focus:outline-none focus:ring-2 focus:ring-blue-500"
                      disabled={node.id === 1} // 平衡节点类型不可更改
                    >
                      {getNodeTypeOptions().map(option => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                  </td>
                  <td className="border border-gray-200 px-3 py-2">
                    <input
                      type="number"
                      step="0.01"
                      value={node.voltage.toFixed(3)}
                      onChange={(e) => handleInputChange(node.id, 'voltage', e.target.value)}
                      className="w-full px-2 py-1 border border-gray-300 rounded text-xs focus:outline-none focus:ring-2 focus:ring-blue-500"
                      disabled={node.type === 'slack' || node.type === 'pv'} // 平衡节点和PV节点电压固定
                    />
                  </td>
                  <td className="border border-gray-200 px-3 py-2">
                    <input
                      type="number"
                      step="0.01"
                      value={node.activePower.toFixed(3)}
                      onChange={(e) => handleInputChange(node.id, 'activePower', e.target.value)}
                      className="w-full px-2 py-1 border border-gray-300 rounded text-xs focus:outline-none focus:ring-2 focus:ring-blue-500"
                      disabled={node.type === 'slack'} // 平衡节点有功功率由系统确定
                    />
                  </td>
                  <td className="border border-gray-200 px-3 py-2">
                    <input
                      type="number"
                      step="0.01"
                      value={node.reactivePower.toFixed(3)}
                      onChange={(e) => handleInputChange(node.id, 'reactivePower', e.target.value)}
                      className="w-full px-2 py-1 border border-gray-300 rounded text-xs focus:outline-none focus:ring-2 focus:ring-blue-500"
                      disabled={node.type === 'slack' || node.type === 'pv'} // 平衡节点和PV节点无功功率由系统确定
                    />
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* 参数说明 */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="font-medium text-blue-800 mb-2 flex items-center gap-2">
            <Zap className="w-4 h-4" />
            参数说明
          </h4>
          <div className="text-sm text-blue-700 space-y-1">
            <p><strong>平衡节点：</strong>电压幅值和相角固定，有功和无功功率由系统平衡确定</p>
            <p><strong>PV节点：</strong>有功功率和电压幅值给定，无功功率和相角由计算确定</p>
            <p><strong>PQ节点：</strong>有功和无功功率给定，电压幅值和相角由计算确定</p>
            <p><strong>标幺值：</strong>以系统基准值为基础的相对值，便于不同电压等级系统的统一分析</p>
          </div>
        </div>

        {/* 计算按钮 */}
        <div className="flex flex-col gap-4">
          <button
            onClick={onCalculate}
            disabled={isCalculating}
            className={`w-full flex items-center justify-center gap-2 px-6 py-3 rounded-lg font-medium transition-colors ${
              isCalculating
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-blue-600 hover:bg-blue-700 active:bg-blue-800'
            } text-white`}
          >
            <Power className="w-5 h-5" />
            {isCalculating ? '计算中...' : '开始潮流计算'}
          </button>

          {/* 快速设置按钮 */}
          <div className="grid grid-cols-2 gap-2">
            <button
              onClick={() => {
                // 重置为默认值
                onNodeChange(2, 'activePower', -0.5);
                onNodeChange(2, 'reactivePower', -0.2);
                onNodeChange(3, 'activePower', -0.3);
                onNodeChange(3, 'reactivePower', -0.1);
              }}
              className="px-3 py-2 text-sm border border-gray-300 rounded hover:bg-gray-50 transition-colors"
            >
              恢复默认
            </button>
            <button
              onClick={() => {
                // 设置重负荷场景
                onNodeChange(2, 'activePower', -0.8);
                onNodeChange(2, 'reactivePower', -0.3);
                onNodeChange(3, 'activePower', -0.6);
                onNodeChange(3, 'reactivePower', -0.2);
              }}
              className="px-3 py-2 text-sm border border-gray-300 rounded hover:bg-gray-50 transition-colors"
            >
              重负荷
            </button>
          </div>
        </div>

        {/* 操作提示 */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
          <p className="text-sm text-yellow-800">
            <strong>提示：</strong>修改参数后点击"开始潮流计算"查看结果。负值表示负荷（消耗功率），正值表示发电（提供功率）。
          </p>
        </div>
      </div>
    </div>
  );
};

export default ParameterPanel;