// 潮流计算核心算法
// 实现简化的牛顿-拉夫逊算法

export interface Node {
  id: number;
  type: 'slack' | 'pv' | 'pq'; // 平衡节点、PV节点、PQ节点
  voltage: number; // 电压幅值 (p.u.)
  angle: number; // 电压相角 (弧度)
  activePower: number; // 有功功率 (p.u.)
  reactivePower: number; // 无功功率 (p.u.)
  name: string;
}

export interface Branch {
  from: number;
  to: number;
  resistance: number; // 电阻 (p.u.)
  reactance: number; // 电抗 (p.u.)
  susceptance: number; // 电纳 (p.u.)
}

export interface PowerFlowResult {
  nodes: Node[];
  iterations: number;
  converged: boolean;
  maxMismatch: number;
  iterationHistory: IterationData[];
}

export interface IterationData {
  iteration: number;
  maxMismatch: number;
  voltages: { id: number; voltage: number; angle: number }[];
}

// 复数类
class Complex {
  constructor(public real: number, public imag: number) {}

  static fromPolar(magnitude: number, angle: number): Complex {
    return new Complex(
      magnitude * Math.cos(angle),
      magnitude * Math.sin(angle)
    );
  }

  magnitude(): number {
    return Math.sqrt(this.real * this.real + this.imag * this.imag);
  }

  angle(): number {
    return Math.atan2(this.imag, this.real);
  }

  add(other: Complex): Complex {
    return new Complex(this.real + other.real, this.imag + other.imag);
  }

  multiply(other: Complex): Complex {
    return new Complex(
      this.real * other.real - this.imag * other.imag,
      this.real * other.imag + this.imag * other.real
    );
  }

  conjugate(): Complex {
    return new Complex(this.real, -this.imag);
  }
}

// 构建导纳矩阵
function buildAdmittanceMatrix(nodes: Node[], branches: Branch[]): Complex[][] {
  const n = nodes.length;
  const Y = Array(n).fill(null).map(() => Array(n).fill(null).map(() => new Complex(0, 0)));

  // 添加支路导纳
  branches.forEach(branch => {
    const i = branch.from - 1; // 转换为0索引
    const j = branch.to - 1;

    // 检查节点索引是否有效
    if (i < 0 || i >= n || j < 0 || j >= n) {
      console.warn(`支路节点索引无效: from=${branch.from}, to=${branch.to}`);
      return;
    }

    const impedance = new Complex(branch.resistance, branch.reactance);
    const impedanceMagnitudeSquared = impedance.real * impedance.real + impedance.imag * impedance.imag;

    // 检查阻抗是否为零
    if (impedanceMagnitudeSquared < 1e-12) {
      console.warn(`支路阻抗过小或为零: R=${branch.resistance}, X=${branch.reactance}`);
      return;
    }

    const admittance = new Complex(
      impedance.real / impedanceMagnitudeSquared,
      -impedance.imag / impedanceMagnitudeSquared
    );

    // 非对角元素（互导纳）
    Y[i][j] = Y[i][j].add(new Complex(-admittance.real, -admittance.imag));
    Y[j][i] = Y[j][i].add(new Complex(-admittance.real, -admittance.imag));

    // 对角元素（自导纳）
    Y[i][i] = Y[i][i].add(admittance);
    Y[j][j] = Y[j][j].add(admittance);

    // 添加线路对地电纳（如果有）
    if (branch.susceptance !== 0) {
      const halfSusceptance = new Complex(0, branch.susceptance / 2);
      Y[i][i] = Y[i][i].add(halfSusceptance);
      Y[j][j] = Y[j][j].add(halfSusceptance);
    }
  });

  return Y;
}

// 计算功率不平衡量
function calculateMismatch(nodes: Node[], Y: Complex[][]): { deltaP: number[]; deltaQ: number[] } {
  const n = nodes.length;
  const deltaP: number[] = [];
  const deltaQ: number[] = [];

  for (let i = 0; i < n; i++) {
    if (nodes[i].type === 'slack') continue;

    let Pcalc = 0;
    let Qcalc = 0;

    for (let j = 0; j < n; j++) {
      const Yij = Y[i][j];
      const Vi = nodes[i].voltage;
      const Vj = nodes[j].voltage;
      const thetaij = nodes[i].angle - nodes[j].angle;

      Pcalc += Vi * Vj * (Yij.real * Math.cos(thetaij) + Yij.imag * Math.sin(thetaij));
      Qcalc += Vi * Vj * (Yij.imag * Math.cos(thetaij) - Yij.real * Math.sin(thetaij));
    }

    deltaP.push(nodes[i].activePower - Pcalc);
    
    if (nodes[i].type === 'pq') {
      deltaQ.push(nodes[i].reactivePower - Qcalc);
    }
  }

  return { deltaP, deltaQ };
}

// 构建雅可比矩阵 - 完全重写版本
function buildJacobian(nodes: Node[], Y: Complex[][]): number[][] {
  const n = nodes.length;
  const nonSlackNodes = nodes.filter(node => node.type !== 'slack');
  const pqNodes = nodes.filter(node => node.type === 'pq');
  const size = nonSlackNodes.length + pqNodes.length;

  if (size === 0) return [];

  const J = Array(size).fill(null).map(() => Array(size).fill(0));

  // 创建节点索引映射
  const nodeToRowMap = new Map();
  const nodeToColMap = new Map();

  let rowIndex = 0;
  let colIndex = 0;

  // 为非平衡节点创建行索引映射
  for (let i = 0; i < n; i++) {
    if (nodes[i].type !== 'slack') {
      nodeToRowMap.set(i, rowIndex++);
    }
  }

  // 为非平衡节点创建列索引映射（角度变量）
  for (let i = 0; i < n; i++) {
    if (nodes[i].type !== 'slack') {
      nodeToColMap.set(i, colIndex++);
    }
  }

  // 为PQ节点创建列索引映射（电压变量）
  for (let i = 0; i < n; i++) {
    if (nodes[i].type === 'pq') {
      nodeToColMap.set(i + n, colIndex++); // 使用i+n作为电压变量的键
    }
  }

  // H矩阵: ∂P/∂θ
  for (let i = 0; i < n; i++) {
    if (nodes[i].type === 'slack') continue;
    const row = nodeToRowMap.get(i);

    for (let j = 0; j < n; j++) {
      if (nodes[j].type === 'slack') continue;
      const col = nodeToColMap.get(j);

      if (i === j) {
        // 对角元素
        let sum = 0;
        for (let k = 0; k < n; k++) {
          if (k !== i) {
            const thetaik = nodes[i].angle - nodes[k].angle;
            sum += nodes[k].voltage * (Y[i][k].real * Math.sin(thetaik) - Y[i][k].imag * Math.cos(thetaik));
          }
        }
        J[row][col] = -nodes[i].voltage * sum;
      } else {
        // 非对角元素
        const thetaij = nodes[i].angle - nodes[j].angle;
        J[row][col] = nodes[i].voltage * nodes[j].voltage *
          (Y[i][j].real * Math.sin(thetaij) - Y[i][j].imag * Math.cos(thetaij));
      }
    }
  }

  // N矩阵: ∂P/∂V (仅对PQ节点)
  for (let i = 0; i < n; i++) {
    if (nodes[i].type === 'slack') continue;
    const row = nodeToRowMap.get(i);

    for (let j = 0; j < n; j++) {
      if (nodes[j].type !== 'pq') continue;
      const col = nodeToColMap.get(j + n);

      if (i === j) {
        // 对角元素
        let sum = 0;
        for (let k = 0; k < n; k++) {
          const thetaik = nodes[i].angle - nodes[k].angle;
          sum += nodes[k].voltage * (Y[i][k].real * Math.cos(thetaik) + Y[i][k].imag * Math.sin(thetaik));
        }
        J[row][col] = sum + nodes[i].voltage * Y[i][i].real;
      } else {
        // 非对角元素
        const thetaij = nodes[i].angle - nodes[j].angle;
        J[row][col] = nodes[i].voltage *
          (Y[i][j].real * Math.cos(thetaij) + Y[i][j].imag * Math.sin(thetaij));
      }
    }
  }

  // M矩阵: ∂Q/∂θ (仅对PQ节点)
  for (let i = 0; i < n; i++) {
    if (nodes[i].type !== 'pq') continue;
    const row = nodeToRowMap.get(i) + nonSlackNodes.length; // Q方程在P方程之后

    for (let j = 0; j < n; j++) {
      if (nodes[j].type === 'slack') continue;
      const col = nodeToColMap.get(j);

      if (i === j) {
        // 对角元素
        let sum = 0;
        for (let k = 0; k < n; k++) {
          if (k !== i) {
            const thetaik = nodes[i].angle - nodes[k].angle;
            sum += nodes[k].voltage * (Y[i][k].real * Math.cos(thetaik) + Y[i][k].imag * Math.sin(thetaik));
          }
        }
        J[row][col] = nodes[i].voltage * sum;
      } else {
        // 非对角元素
        const thetaij = nodes[i].angle - nodes[j].angle;
        J[row][col] = -nodes[i].voltage * nodes[j].voltage *
          (Y[i][j].real * Math.cos(thetaij) + Y[i][j].imag * Math.sin(thetaij));
      }
    }
  }

  // L矩阵: ∂Q/∂V (仅对PQ节点)
  for (let i = 0; i < n; i++) {
    if (nodes[i].type !== 'pq') continue;
    const row = nodeToRowMap.get(i) + nonSlackNodes.length; // Q方程在P方程之后

    for (let j = 0; j < n; j++) {
      if (nodes[j].type !== 'pq') continue;
      const col = nodeToColMap.get(j + n);

      if (i === j) {
        // 对角元素
        let sum = 0;
        for (let k = 0; k < n; k++) {
          const thetaik = nodes[i].angle - nodes[k].angle;
          sum += nodes[k].voltage * (Y[i][k].imag * Math.cos(thetaik) - Y[i][k].real * Math.sin(thetaik));
        }
        J[row][col] = sum - nodes[i].voltage * Y[i][i].imag;
      } else {
        // 非对角元素
        const thetaij = nodes[i].angle - nodes[j].angle;
        J[row][col] = nodes[i].voltage *
          (Y[i][j].imag * Math.cos(thetaij) - Y[i][j].real * Math.sin(thetaij));
      }
    }
  }

  return J;
}

// 改进的线性方程组求解（LU分解）
function solveLinearSystem(A: number[][], b: number[]): number[] {
  const n = A.length;
  const x = new Array(n).fill(0);

  // 检查矩阵是否为空
  if (n === 0) return x;

  // 简化的高斯消元法 - 改进数值稳定性
  for (let i = 0; i < n; i++) {
    // 寻找主元
    let maxRow = i;
    for (let k = i + 1; k < n; k++) {
      if (Math.abs(A[k][i]) > Math.abs(A[maxRow][i])) {
        maxRow = k;
      }
    }

    // 检查主元是否太小
    if (Math.abs(A[maxRow][i]) < 1e-12) {
      console.warn(`雅可比矩阵奇异或接近奇异，第${i}行主元过小: ${A[maxRow][i]}`);
      // 设置一个小的非零值避免除零
      A[maxRow][i] = A[maxRow][i] >= 0 ? 1e-12 : -1e-12;
    }

    // 交换行
    if (maxRow !== i) {
      [A[i], A[maxRow]] = [A[maxRow], A[i]];
      [b[i], b[maxRow]] = [b[maxRow], b[i]];
    }

    // 消元
    for (let k = i + 1; k < n; k++) {
      const factor = A[k][i] / A[i][i];
      for (let j = i; j < n; j++) {
        A[k][j] -= factor * A[i][j];
      }
      b[k] -= factor * b[i];
    }
  }

  // 回代
  for (let i = n - 1; i >= 0; i--) {
    x[i] = b[i];
    for (let j = i + 1; j < n; j++) {
      x[i] -= A[i][j] * x[j];
    }
    x[i] /= A[i][i];
  }

  return x;
}

// 主要的潮流计算函数
export function calculatePowerFlow(nodes: Node[], branches: Branch[]): PowerFlowResult {
  const maxIterations = 50; // 增加最大迭代次数
  const tolerance = 1e-4; // 放宽收敛精度
  const iterationHistory: IterationData[] = [];

  // 复制节点数据以避免修改原始数据
  const workingNodes = nodes.map(node => ({ ...node }));

  // 验证输入数据
  if (workingNodes.length === 0 || branches.length === 0) {
    throw new Error('节点或支路数据为空');
  }

  // 检查是否有平衡节点
  const slackNodes = workingNodes.filter(node => node.type === 'slack');
  if (slackNodes.length === 0) {
    throw new Error('系统中必须有至少一个平衡节点');
  }

  // 构建导纳矩阵
  const Y = buildAdmittanceMatrix(workingNodes, branches);

  let iteration = 0;
  let converged = false;
  let maxMismatch = Infinity;

  while (iteration < maxIterations && !converged) {
    // 计算功率不平衡量
    const { deltaP, deltaQ } = calculateMismatch(workingNodes, Y);
    const mismatch = [...deltaP, ...deltaQ];

    // 检查是否有NaN或无穷大
    if (mismatch.some(val => !isFinite(val))) {
      console.error('功率不平衡量包含非有限值:', mismatch);
      break;
    }

    maxMismatch = Math.max(...mismatch.map(Math.abs));

    // 记录迭代历史
    iterationHistory.push({
      iteration: iteration + 1,
      maxMismatch,
      voltages: workingNodes.map(node => ({
        id: node.id,
        voltage: node.voltage,
        angle: node.angle
      }))
    });

    if (maxMismatch < tolerance) {
      converged = true;
      break;
    }

    // 构建雅可比矩阵
    const J = buildJacobian(workingNodes, Y);

    // 检查雅可比矩阵是否有效
    if (J.length === 0 || J.some(row => row.some(val => !isFinite(val)))) {
      console.error('雅可比矩阵无效');
      break;
    }

    // 求解修正量
    const corrections = solveLinearSystem(J, mismatch);

    // 检查修正量是否有效
    if (corrections.some(val => !isFinite(val))) {
      console.error('修正量包含非有限值:', corrections);
      break;
    }

    // 限制修正量的大小以提高稳定性
    const maxAngleCorrection = 0.1; // 限制角度修正量（弧度）
    const maxVoltageCorrection = 0.05; // 限制电压修正量（p.u.）

    // 更新节点电压
    let corrIndex = 0;
    for (let i = 0; i < workingNodes.length; i++) {
      if (workingNodes[i].type !== 'slack') {
        const angleCorr = Math.max(-maxAngleCorrection,
                                  Math.min(maxAngleCorrection, corrections[corrIndex] || 0));
        workingNodes[i].angle += angleCorr;
        corrIndex++;
      }
      if (workingNodes[i].type === 'pq') {
        const voltageCorr = Math.max(-maxVoltageCorrection,
                                    Math.min(maxVoltageCorrection, corrections[corrIndex] || 0));
        workingNodes[i].voltage += voltageCorr;
        // 确保电压不会变成负值或过大
        workingNodes[i].voltage = Math.max(0.1, Math.min(2.0, workingNodes[i].voltage));
        corrIndex++;
      }
    }

    iteration++;
  }

  return {
    nodes: workingNodes,
    iterations: iteration,
    converged,
    maxMismatch,
    iterationHistory
  };
}

// 获取默认的3节点系统
export function getDefaultSystem(): { nodes: Node[]; branches: Branch[] } {
  const nodes: Node[] = [
    {
      id: 1,
      type: 'slack',
      voltage: 1.0,
      angle: 0,
      activePower: 0,
      reactivePower: 0,
      name: '平衡节点'
    },
    {
      id: 2,
      type: 'pq',
      voltage: 1.0,
      angle: 0,
      activePower: -0.5, // 负荷
      reactivePower: -0.2,
      name: '负荷节点1'
    },
    {
      id: 3,
      type: 'pq',
      voltage: 1.0,
      angle: 0,
      activePower: -0.3, // 负荷
      reactivePower: -0.1,
      name: '负荷节点2'
    }
  ];

  const branches: Branch[] = [
    {
      from: 1,
      to: 2,
      resistance: 0.02,
      reactance: 0.06,
      susceptance: 0
    },
    {
      from: 1,
      to: 3,
      resistance: 0.03,
      reactance: 0.09,
      susceptance: 0
    },
    {
      from: 2,
      to: 3,
      resistance: 0.025,
      reactance: 0.075,
      susceptance: 0
    }
  ];

  return { nodes, branches };
}
