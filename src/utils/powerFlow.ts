// 潮流计算核心算法
// 实现简化的牛顿-拉夫逊算法

export interface Node {
  id: number;
  type: 'slack' | 'pv' | 'pq'; // 平衡节点、PV节点、PQ节点
  voltage: number; // 电压幅值 (p.u.)
  angle: number; // 电压相角 (弧度)
  activePower: number; // 有功功率 (p.u.)
  reactivePower: number; // 无功功率 (p.u.)
  name: string;
}

export interface Branch {
  from: number;
  to: number;
  resistance: number; // 电阻 (p.u.)
  reactance: number; // 电抗 (p.u.)
  susceptance: number; // 电纳 (p.u.)
}

export interface PowerFlowResult {
  nodes: Node[];
  iterations: number;
  converged: boolean;
  maxMismatch: number;
  iterationHistory: IterationData[];
}

export interface IterationData {
  iteration: number;
  maxMismatch: number;
  voltages: { id: number; voltage: number; angle: number }[];
}

// 复数类
class Complex {
  constructor(public real: number, public imag: number) {}

  static fromPolar(magnitude: number, angle: number): Complex {
    return new Complex(
      magnitude * Math.cos(angle),
      magnitude * Math.sin(angle)
    );
  }

  magnitude(): number {
    return Math.sqrt(this.real * this.real + this.imag * this.imag);
  }

  angle(): number {
    return Math.atan2(this.imag, this.real);
  }

  add(other: Complex): Complex {
    return new Complex(this.real + other.real, this.imag + other.imag);
  }

  multiply(other: Complex): Complex {
    return new Complex(
      this.real * other.real - this.imag * other.imag,
      this.real * other.imag + this.imag * other.real
    );
  }

  conjugate(): Complex {
    return new Complex(this.real, -this.imag);
  }
}

// 构建导纳矩阵
function buildAdmittanceMatrix(nodes: Node[], branches: Branch[]): Complex[][] {
  const n = nodes.length;
  const Y = Array(n).fill(null).map(() => Array(n).fill(new Complex(0, 0)));

  // 初始化对角元素（节点自导纳）
  for (let i = 0; i < n; i++) {
    Y[i][i] = new Complex(0, 0);
  }

  // 添加支路导纳
  branches.forEach(branch => {
    const i = branch.from - 1; // 转换为0索引
    const j = branch.to - 1;
    
    const impedance = new Complex(branch.resistance, branch.reactance);
    const admittance = new Complex(
      impedance.real / (impedance.real * impedance.real + impedance.imag * impedance.imag),
      -impedance.imag / (impedance.real * impedance.real + impedance.imag * impedance.imag)
    );

    // 非对角元素
    Y[i][j] = Y[i][j].add(new Complex(-admittance.real, -admittance.imag));
    Y[j][i] = Y[j][i].add(new Complex(-admittance.real, -admittance.imag));

    // 对角元素
    Y[i][i] = Y[i][i].add(admittance);
    Y[j][j] = Y[j][j].add(admittance);
  });

  return Y;
}

// 计算功率不平衡量
function calculateMismatch(nodes: Node[], Y: Complex[][]): { deltaP: number[]; deltaQ: number[] } {
  const n = nodes.length;
  const deltaP: number[] = [];
  const deltaQ: number[] = [];

  for (let i = 0; i < n; i++) {
    if (nodes[i].type === 'slack') continue;

    let Pcalc = 0;
    let Qcalc = 0;

    for (let j = 0; j < n; j++) {
      const Yij = Y[i][j];
      const Vi = nodes[i].voltage;
      const Vj = nodes[j].voltage;
      const thetaij = nodes[i].angle - nodes[j].angle;

      Pcalc += Vi * Vj * (Yij.real * Math.cos(thetaij) + Yij.imag * Math.sin(thetaij));
      Qcalc += Vi * Vj * (Yij.imag * Math.cos(thetaij) - Yij.real * Math.sin(thetaij));
    }

    deltaP.push(nodes[i].activePower - Pcalc);
    
    if (nodes[i].type === 'pq') {
      deltaQ.push(nodes[i].reactivePower - Qcalc);
    }
  }

  return { deltaP, deltaQ };
}

// 构建雅可比矩阵 - 修复版本
function buildJacobian(nodes: Node[], Y: Complex[][]): number[][] {
  const n = nodes.length;
  const nonSlackNodes = nodes.filter(node => node.type !== 'slack');
  const pqNodes = nodes.filter(node => node.type === 'pq');
  const size = nonSlackNodes.length + pqNodes.length;
  
  const J = Array(size).fill(null).map(() => Array(size).fill(0));
  
  let row = 0;
  
  // P方程对θ的偏导数 (H矩阵)
  for (let i = 0; i < n; i++) {
    if (nodes[i].type === 'slack') continue;
    
    let col = 0;
    for (let j = 0; j < n; j++) {
      if (nodes[j].type === 'slack') continue;
      
      if (i === j) {
        // 对角元素
        let sum = 0;
        for (let k = 0; k < n; k++) {
          if (k !== i) {
            const thetaik = nodes[i].angle - nodes[k].angle;
            sum += nodes[k].voltage * (Y[i][k].real * Math.sin(thetaik) - Y[i][k].imag * Math.cos(thetaik));
          }
        }
        J[row][col] = -nodes[i].voltage * sum;
      } else {
        // 非对角元素
        const thetaij = nodes[i].angle - nodes[j].angle;
        J[row][col] = nodes[i].voltage * nodes[j].voltage * 
          (Y[i][j].real * Math.sin(thetaij) - Y[i][j].imag * Math.cos(thetaij));
      }
      col++;
    }
    row++;
  }
  
  // P方程对V的偏导数 (N矩阵) - 仅对PQ节点
  row = 0;
  for (let i = 0; i < n; i++) {
    if (nodes[i].type === 'slack') continue;
    
    let col = nonSlackNodes.length;
    for (let j = 0; j < n; j++) {
      if (nodes[j].type !== 'pq') continue;
      
      if (i === j) {
        // 对角元素
        let sum = 0;
        for (let k = 0; k < n; k++) {
          const thetaik = nodes[i].angle - nodes[k].angle;
          sum += nodes[k].voltage * (Y[i][k].real * Math.cos(thetaik) + Y[i][k].imag * Math.sin(thetaik));
        }
        J[row][col] = sum + nodes[i].voltage * Y[i][i].real;
      } else {
        // 非对角元素
        const thetaij = nodes[i].angle - nodes[j].angle;
        J[row][col] = nodes[i].voltage * 
          (Y[i][j].real * Math.cos(thetaij) + Y[i][j].imag * Math.sin(thetaij));
      }
      col++;
    }
    row++;
  }
  
  // Q方程对θ的偏导数 (M矩阵) - 仅对PQ节点
  for (let i = 0; i < n; i++) {
    if (nodes[i].type !== 'pq') continue;
    
    let col = 0;
    for (let j = 0; j < n; j++) {
      if (nodes[j].type === 'slack') continue;
      
      if (i === j) {
        // 对角元素
        let sum = 0;
        for (let k = 0; k < n; k++) {
          if (k !== i) {
            const thetaik = nodes[i].angle - nodes[k].angle;
            sum += nodes[k].voltage * (Y[i][k].real * Math.cos(thetaik) + Y[i][k].imag * Math.sin(thetaik));
          }
        }
        J[row][col] = nodes[i].voltage * sum;
      } else {
        // 非对角元素
        const thetaij = nodes[i].angle - nodes[j].angle;
        J[row][col] = -nodes[i].voltage * nodes[j].voltage * 
          (Y[i][j].real * Math.cos(thetaij) + Y[i][j].imag * Math.sin(thetaij));
      }
      col++;
    }
    row++;
  }
  
  // Q方程对V的偏导数 (L矩阵) - 仅对PQ节点
  row = nonSlackNodes.length;
  for (let i = 0; i < n; i++) {
    if (nodes[i].type !== 'pq') continue;
    
    let col = nonSlackNodes.length;
    for (let j = 0; j < n; j++) {
      if (nodes[j].type !== 'pq') continue;
      
      if (i === j) {
        // 对角元素
        let sum = 0;
        for (let k = 0; k < n; k++) {
          const thetaik = nodes[i].angle - nodes[k].angle;
          sum += nodes[k].voltage * (Y[i][k].imag * Math.cos(thetaik) - Y[i][k].real * Math.sin(thetaik));
        }
        J[row][col] = sum - nodes[i].voltage * Y[i][i].imag;
      } else {
        // 非对角元素
        const thetaij = nodes[i].angle - nodes[j].angle;
        J[row][col] = nodes[i].voltage * 
          (Y[i][j].imag * Math.cos(thetaij) - Y[i][j].real * Math.sin(thetaij));
      }
      col++;
    }
    row++;
  }
  
  return J;
}

// 改进的线性方程组求解（LU分解）
function solveLinearSystem(A: number[][], b: number[]): number[] {
  const n = A.length;
  const x = new Array(n).fill(0);
  
  // 简化的高斯消元法
  for (let i = 0; i < n; i++) {
    // 寻找主元
    let maxRow = i;
    for (let k = i + 1; k < n; k++) {
      if (Math.abs(A[k][i]) > Math.abs(A[maxRow][i])) {
        maxRow = k;
      }
    }
    
    // 交换行
    if (maxRow !== i) {
      [A[i], A[maxRow]] = [A[maxRow], A[i]];
      [b[i], b[maxRow]] = [b[maxRow], b[i]];
    }
    
    // 消元
    for (let k = i + 1; k < n; k++) {
      if (Math.abs(A[i][i]) > 1e-10) {
        const factor = A[k][i] / A[i][i];
        for (let j = i; j < n; j++) {
          A[k][j] -= factor * A[i][j];
        }
        b[k] -= factor * b[i];
      }
    }
  }
  
  // 回代
  for (let i = n - 1; i >= 0; i--) {
    x[i] = b[i];
    for (let j = i + 1; j < n; j++) {
      x[i] -= A[i][j] * x[j];
    }
    if (Math.abs(A[i][i]) > 1e-10) {
      x[i] /= A[i][i];
    }
  }
  
  return x;
}

// 主要的潮流计算函数
export function calculatePowerFlow(nodes: Node[], branches: Branch[]): PowerFlowResult {
  const maxIterations = 20;
  const tolerance = 1e-6;
  const iterationHistory: IterationData[] = [];
  
  // 复制节点数据以避免修改原始数据
  const workingNodes = nodes.map(node => ({ ...node }));
  
  // 构建导纳矩阵
  const Y = buildAdmittanceMatrix(workingNodes, branches);
  
  let iteration = 0;
  let converged = false;
  let maxMismatch = Infinity;
  
  while (iteration < maxIterations && !converged) {
    // 计算功率不平衡量
    const { deltaP, deltaQ } = calculateMismatch(workingNodes, Y);
    const mismatch = [...deltaP, ...deltaQ];
    
    maxMismatch = Math.max(...mismatch.map(Math.abs));
    
    // 记录迭代历史
    iterationHistory.push({
      iteration: iteration + 1,
      maxMismatch,
      voltages: workingNodes.map(node => ({
        id: node.id,
        voltage: node.voltage,
        angle: node.angle
      }))
    });
    
    if (maxMismatch < tolerance) {
      converged = true;
      break;
    }
    
    // 构建雅可比矩阵
    const J = buildJacobian(workingNodes, Y);
    
    // 求解修正量
    const corrections = solveLinearSystem(J, mismatch);
    
    // 更新节点电压
    let corrIndex = 0;
    for (let i = 0; i < workingNodes.length; i++) {
      if (workingNodes[i].type !== 'slack') {
        workingNodes[i].angle += corrections[corrIndex] || 0;
        corrIndex++;
      }
      if (workingNodes[i].type === 'pq') {
        workingNodes[i].voltage += corrections[corrIndex] || 0;
        corrIndex++;
      }
    }
    
    iteration++;
  }
  
  return {
    nodes: workingNodes,
    iterations: iteration,
    converged,
    maxMismatch,
    iterationHistory
  };
}

// 获取默认的3节点系统
export function getDefaultSystem(): { nodes: Node[]; branches: Branch[] } {
  const nodes: Node[] = [
    {
      id: 1,
      type: 'slack',
      voltage: 1.0,
      angle: 0,
      activePower: 0,
      reactivePower: 0,
      name: '平衡节点'
    },
    {
      id: 2,
      type: 'pq',
      voltage: 1.0,
      angle: 0,
      activePower: -0.5, // 负荷
      reactivePower: -0.2,
      name: '负荷节点1'
    },
    {
      id: 3,
      type: 'pq',
      voltage: 1.0,
      angle: 0,
      activePower: -0.3, // 负荷
      reactivePower: -0.1,
      name: '负荷节点2'
    }
  ];

  const branches: Branch[] = [
    {
      from: 1,
      to: 2,
      resistance: 0.02,
      reactance: 0.06,
      susceptance: 0
    },
    {
      from: 1,
      to: 3,
      resistance: 0.03,
      reactance: 0.09,
      susceptance: 0
    },
    {
      from: 2,
      to: 3,
      resistance: 0.025,
      reactance: 0.075,
      susceptance: 0
    }
  ];

  return { nodes, branches };
}
