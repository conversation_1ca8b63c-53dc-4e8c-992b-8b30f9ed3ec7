// 潮流计算测试文件
import { Node, Branch, calculatePowerFlow, getDefaultSystem } from './powerFlow';

// 测试用例1：基础3节点系统
export function testBasicSystem() {
  console.log('=== 测试基础3节点系统 ===');
  const { nodes, branches } = getDefaultSystem();
  
  console.log('输入数据:');
  console.log('节点:', nodes);
  console.log('支路:', branches);
  
  const result = calculatePowerFlow(nodes, branches);
  
  console.log('计算结果:');
  console.log(`收敛状态: ${result.converged ? '收敛' : '不收敛'}`);
  console.log(`迭代次数: ${result.iterations}`);
  console.log(`最大不平衡量: ${result.maxMismatch.toExponential(3)}`);
  
  result.nodes.forEach(node => {
    console.log(`节点${node.id}: V=${node.voltage.toFixed(4)} p.u., θ=${(node.angle * 180 / Math.PI).toFixed(2)}°`);
  });
  
  return result;
}

// 测试用例2：重负荷系统
export function testHeavyLoadSystem() {
  console.log('\n=== 测试重负荷系统 ===');
  
  const nodes: Node[] = [
    { id: 1, type: 'slack', voltage: 1.0, angle: 0, activePower: 0, reactivePower: 0, name: '平衡节点' },
    { id: 2, type: 'pq', voltage: 1.0, angle: 0, activePower: -0.9, reactivePower: -0.3, name: '重负荷节点' },
    { id: 3, type: 'pq', voltage: 1.0, angle: 0, activePower: -0.6, reactivePower: -0.2, name: '负荷节点' }
  ];

  const branches: Branch[] = [
    { from: 1, to: 2, resistance: 0.02, reactance: 0.06, susceptance: 0 },
    { from: 1, to: 3, resistance: 0.03, reactance: 0.09, susceptance: 0 },
    { from: 2, to: 3, resistance: 0.025, reactance: 0.075, susceptance: 0 }
  ];
  
  const result = calculatePowerFlow(nodes, branches);
  
  console.log('计算结果:');
  console.log(`收敛状态: ${result.converged ? '收敛' : '不收敛'}`);
  console.log(`迭代次数: ${result.iterations}`);
  console.log(`最大不平衡量: ${result.maxMismatch.toExponential(3)}`);
  
  result.nodes.forEach(node => {
    console.log(`节点${node.id}: V=${node.voltage.toFixed(4)} p.u., θ=${(node.angle * 180 / Math.PI).toFixed(2)}°`);
  });
  
  return result;
}

// 测试用例3：包含PV节点的系统
export function testPVNodeSystem() {
  console.log('\n=== 测试PV节点系统 ===');
  
  const nodes: Node[] = [
    { id: 1, type: 'slack', voltage: 1.0, angle: 0, activePower: 0, reactivePower: 0, name: '平衡节点' },
    { id: 2, type: 'pv', voltage: 1.02, angle: 0, activePower: 0.4, reactivePower: 0, name: 'PV发电机' },
    { id: 3, type: 'pq', voltage: 1.0, angle: 0, activePower: -0.6, reactivePower: -0.2, name: '负荷节点' }
  ];

  const branches: Branch[] = [
    { from: 1, to: 2, resistance: 0.015, reactance: 0.045, susceptance: 0 },
    { from: 1, to: 3, resistance: 0.025, reactance: 0.075, susceptance: 0 },
    { from: 2, to: 3, resistance: 0.02, reactance: 0.06, susceptance: 0 }
  ];
  
  const result = calculatePowerFlow(nodes, branches);
  
  console.log('计算结果:');
  console.log(`收敛状态: ${result.converged ? '收敛' : '不收敛'}`);
  console.log(`迭代次数: ${result.iterations}`);
  console.log(`最大不平衡量: ${result.maxMismatch.toExponential(3)}`);
  
  result.nodes.forEach(node => {
    console.log(`节点${node.id}: V=${node.voltage.toFixed(4)} p.u., θ=${(node.angle * 180 / Math.PI).toFixed(2)}°`);
  });
  
  return result;
}

// 测试用例4：高阻抗线路系统
export function testHighImpedanceSystem() {
  console.log('\n=== 测试高阻抗线路系统 ===');
  
  const nodes: Node[] = [
    { id: 1, type: 'slack', voltage: 1.0, angle: 0, activePower: 0, reactivePower: 0, name: '平衡节点' },
    { id: 2, type: 'pq', voltage: 1.0, angle: 0, activePower: -0.4, reactivePower: -0.15, name: '负荷节点1' },
    { id: 3, type: 'pq', voltage: 1.0, angle: 0, activePower: -0.3, reactivePower: -0.1, name: '负荷节点2' }
  ];

  const branches: Branch[] = [
    { from: 1, to: 2, resistance: 0.1, reactance: 0.3, susceptance: 0 },
    { from: 1, to: 3, resistance: 0.15, reactance: 0.45, susceptance: 0 },
    { from: 2, to: 3, resistance: 0.12, reactance: 0.36, susceptance: 0 }
  ];
  
  const result = calculatePowerFlow(nodes, branches);
  
  console.log('计算结果:');
  console.log(`收敛状态: ${result.converged ? '收敛' : '不收敛'}`);
  console.log(`迭代次数: ${result.iterations}`);
  console.log(`最大不平衡量: ${result.maxMismatch.toExponential(3)}`);
  
  result.nodes.forEach(node => {
    console.log(`节点${node.id}: V=${node.voltage.toFixed(4)} p.u., θ=${(node.angle * 180 / Math.PI).toFixed(2)}°`);
  });
  
  return result;
}

// 运行所有测试
export function runAllTests() {
  console.log('开始潮流计算测试...\n');
  
  const results = {
    basic: testBasicSystem(),
    heavyLoad: testHeavyLoadSystem(),
    pvNode: testPVNodeSystem(),
    highImpedance: testHighImpedanceSystem()
  };
  
  console.log('\n=== 测试总结 ===');
  console.log(`基础系统: ${results.basic.converged ? '✓ 收敛' : '✗ 不收敛'} (${results.basic.iterations}次迭代)`);
  console.log(`重负荷系统: ${results.heavyLoad.converged ? '✓ 收敛' : '✗ 不收敛'} (${results.heavyLoad.iterations}次迭代)`);
  console.log(`PV节点系统: ${results.pvNode.converged ? '✓ 收敛' : '✗ 不收敛'} (${results.pvNode.iterations}次迭代)`);
  console.log(`高阻抗系统: ${results.highImpedance.converged ? '✓ 收敛' : '✗ 不收敛'} (${results.highImpedance.iterations}次迭代)`);
  
  return results;
}
