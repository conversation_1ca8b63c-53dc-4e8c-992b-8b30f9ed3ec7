import React, { useState } from 'react';
import { Node, Branch, PowerFlowResult, calculatePowerFlow, getDefaultSystem } from '../utils/powerFlow';
import NetworkTopology from '../components/NetworkTopology';
import ParameterPanel from '../components/ParameterPanel';
import ResultsPanel from '../components/ResultsPanel';
import AlgorithmExplanation from '../components/AlgorithmExplanation';
import ExampleLibrary from '../components/ExampleLibrary';
import { Zap, BookOpen, Library, Home as HomeIcon } from 'lucide-react';

type TabType = 'main' | 'algorithm' | 'examples';

const Home: React.FC = () => {
  const [activeTab, setActiveTab] = useState<TabType>('main');
  const [nodes, setNodes] = useState<Node[]>(() => getDefaultSystem().nodes);
  const [branches, setBranches] = useState<Branch[]>(() => getDefaultSystem().branches);
  const [result, setResult] = useState<PowerFlowResult | null>(null);
  const [isCalculating, setIsCalculating] = useState(false);

  const handleNodeChange = (nodeId: number, field: keyof Node, value: any) => {
    setNodes(prevNodes => 
      prevNodes.map(node => 
        node.id === nodeId ? { ...node, [field]: value } : node
      )
    );
  };

  const handleCalculate = async () => {
    setIsCalculating(true);
    
    // 模拟计算延迟，让用户看到计算过程
    await new Promise(resolve => setTimeout(resolve, 500));
    
    try {
      const calculationResult = calculatePowerFlow(nodes, branches);
      setResult(calculationResult);
    } catch (error) {
      console.error('潮流计算出错:', error);
      // 这里可以添加错误处理
    } finally {
      setIsCalculating(false);
    }
  };

  const handleLoadExample = (exampleNodes: Node[], exampleBranches: Branch[]) => {
    setNodes(exampleNodes);
    setBranches(exampleBranches);
    setResult(null); // 清除之前的计算结果
    setActiveTab('main'); // 切换到主界面
  };

  const tabs = [
    { id: 'main' as TabType, name: '潮流计算', icon: HomeIcon },
    { id: 'algorithm' as TabType, name: '算法说明', icon: BookOpen },
    { id: 'examples' as TabType, name: '示例库', icon: Library }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部导航 */}
      <header className="bg-white border-b border-gray-200 sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center gap-3">
              <Zap className="w-8 h-8 text-blue-600" />
              <div>
                <h1 className="text-xl font-bold text-gray-900">潮流计算Demo</h1>
                <p className="text-sm text-gray-500">电力系统潮流计算学习工具</p>
              </div>
            </div>
            
            {/* 标签页导航 */}
            <nav className="flex space-x-1">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors ${
                      activeTab === tab.id
                        ? 'bg-blue-100 text-blue-700 border border-blue-200'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    {tab.name}
                  </button>
                );
              })}
            </nav>
          </div>
        </div>
      </header>

      {/* 主要内容区域 */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {activeTab === 'main' && (
          <div className="grid lg:grid-cols-3 gap-8">
            {/* 左侧：网络拓扑图 */}
            <div className="lg:col-span-2">
              <NetworkTopology 
                nodes={result?.nodes || nodes} 
                branches={branches}
                width={700}
                height={500}
              />
            </div>
            
            {/* 右侧：参数面板 */}
            <div className="space-y-6">
              <ParameterPanel
                nodes={nodes}
                onNodeChange={handleNodeChange}
                onCalculate={handleCalculate}
                isCalculating={isCalculating}
              />
            </div>
            
            {/* 底部：计算结果 */}
            <div className="lg:col-span-3">
              <ResultsPanel result={result} />
            </div>
          </div>
        )}
        
        {activeTab === 'algorithm' && (
          <div className="max-w-4xl mx-auto">
            <AlgorithmExplanation />
          </div>
        )}
        
        {activeTab === 'examples' && (
          <div className="max-w-4xl mx-auto">
            <ExampleLibrary onLoadExample={handleLoadExample} />
          </div>
        )}
      </main>

      {/* 页脚 */}
      <footer className="bg-white border-t border-gray-200 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="text-center text-sm text-gray-500">
            <p>潮流计算Demo - 电力系统学习工具</p>
            <p className="mt-1">基于牛顿-拉夫逊算法的JavaScript实现</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Home;